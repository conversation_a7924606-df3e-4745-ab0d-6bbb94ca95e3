import { Link } from "wouter";
import { useTranslation } from "react-i18next";
import { Button } from "../ui/button";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { PhoneCall, ArrowRight, TruckIcon } from "lucide-react";

// Картинки (пути не меняем)
const uProfiles = '/images/products/Aluminum U-Profiles.jpg';
const tProfiles = '/images/products/Aluminum T-Profiles.jpg';
const ledProfiles = '/images/products/Aluminum LED Profile.jpg';
const specialProfiles = '/images/products/Aluminum-Profile-Extrusion.jpg';
const steelPipes = '/images/products/Steel Pipes For Oil and Gas Purpose .png';
const hdpePipes = '/images/products/HDPE pipes (PE pipes) .png';

export function Hero() {
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);

  const sliderImages = [
    { src: uProfiles, alt: "Aluminum U-Profiles" },
    { src: tProfiles, alt: "Aluminum T-Profiles" },
    { src: ledProfiles, alt: "LED Aluminum Profiles" },
    { src: specialProfiles, alt: "Special Aluminum Profiles" },
    { src: steelPipes, alt: "Steel pipes for industrial applications" },
    { src: hdpePipes, alt: "HDPE pipes for infrastructure" }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderImages.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [sliderImages.length]);

  const handleSlideChange = (index: number) => setCurrentSlide(index);

  const scrollToProducts = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const productsSection = document.getElementById('products');
    if (productsSection) {
      window.scrollTo({
        top: productsSection.offsetTop - 80,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section id="home" className="relative min-h-[90vh] sm:min-h-[85vh] overflow-hidden">
      {/* BG SLIDER */}
      <div className="absolute inset-0 w-full h-full overflow-hidden">
        {sliderImages.map((image, index) => (
          <div 
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
              currentSlide === index ? 'opacity-100 z-10' : 'opacity-0 z-0'
            }`}
          >
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-primary/90 via-blue-800/70 to-accent/50 mix-blend-multiply z-10"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle,_rgba(0,0,0,0.12)_0%,_rgba(0,0,0,0.32)_100%)] z-10"></div>
            <img 
              src={image.src} 
              alt={image.alt} 
              loading={index === 0 ? "eager" : "lazy"}
              className="absolute w-full h-full object-cover scale-105 transition-transform duration-10000 ease-linear"
              style={{
                objectPosition: 'center center',
                transform: currentSlide === index ? 'scale(1.05)' : 'scale(1)',
                transition: 'transform 8s ease-in-out'
              }}
            />
          </div>
        ))}
      </div>

      {/* Dots */}
      <div className="absolute bottom-6 left-0 right-0 flex justify-center space-x-2 z-20">
        {sliderImages.map((_, index) => (
          <button
            key={index}
            onClick={() => handleSlideChange(index)}
            className={`transition-all duration-300 ease-in-out h-2 rounded-full ${
              currentSlide === index
                ? 'w-6 bg-white'
                : 'w-3 bg-white/40'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* HERO CONTENT */}
      <div className="container relative z-20 mx-auto px-4 sm:px-6 h-full min-h-[90vh] sm:min-h-[85vh] flex items-center pt-16 pb-14 sm:py-0">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 xs:gap-5 sm:gap-6 lg:gap-12 items-center w-full">
          <div className="lg:col-span-7 z-20">
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block bg-accent/20 backdrop-blur-sm px-4 py-1.5 rounded-full mb-3 text-sm"
            >
              <span className="text-white font-medium tracking-wide">
                {t("hero.tagline", "Engineering a Stronger Tomorrow")}
              </span>
            </motion.div>

            <motion.div 
              className="max-w-3xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
            >
              {/* Заголовок */}
              <motion.h1
                className="text-white text-3xl md:text-5xl lg:text-6xl font-inter font-bold leading-tight mb-3 md:mb-6 max-w-full break-words"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.1 }}
              >
                <span className="block text-white">
                  {t("hero.title", "We Move Materials.")}
                </span>
                <span className="block text-white">
                  {t("hero.title_highlight", "You Build the Future.")}
                </span>
              </motion.h1>

              {/* Подзаголовок */}
              <motion.p 
                className="text-white text-base md:text-xl mb-4 md:mb-8 font-medium max-w-xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                {t("hero.subtitle", "Aluminum profiles and urban infrastructure products from trusted manufacturers")}
              </motion.p>

              {/* Кнопки */}
              <motion.div 
                className="flex flex-col sm:flex-row gap-3 sm:gap-4 items-center mt-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                <Link to="/contact" className="w-full sm:w-auto">
                  <Button className="btn-gradient text-white font-bold text-base px-8 py-4 rounded-lg border-2 border-white/30 shadow-xl hover:scale-105 transition-all duration-300 min-w-[180px] flex items-center justify-center">
                    <PhoneCall className="mr-2 h-5 w-5" /> {t("header.requestQuote", "Request Quote")}
                  </Button>
                </Link>
                <Button 
                  variant="outline"
                  className="bg-white/10 text-white font-bold text-base px-8 py-4 rounded-lg border-2 border-white/40 shadow-xl hover:bg-white/20 hover:scale-105 transition-all duration-300 min-w-[180px] flex items-center justify-center"
                  onClick={scrollToProducts}
                >
                  {t("hero.learnMore", "Learn More")}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </motion.div>
            </motion.div>
          </div>

          {/* Инфо карточка (десктоп) */}
          <motion.div 
            className="mt-6 lg:mt-0 lg:col-span-5 hidden lg:block"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.5 }}
          >
            <div className="relative flex justify-end pr-8">
              <div className="glass-card backdrop-blur-lg bg-white/60 rounded-2xl p-6 shadow-xl rotate-3 transform hover:rotate-0 transition-all duration-500 border border-white/50 w-[260px] animate-bounce-subtle">
                <div className="absolute inset-0 opacity-30 bg-[radial-gradient(ellipse_at_top_right,_rgba(255,255,255,0.9)_0%,_transparent_70%)]"></div>
                <div className="text-center relative z-10">
                  <h3 className="text-xl font-bold text-primary mb-3">{t("hero.productTitle", "Premium Products")}</h3>
                  <p className="text-base text-gray-800 font-medium mb-4">{t("hero.productSubtitle", "High-quality infrastructure solutions")}</p>
                  <div className="w-24 h-1 gradient-primary mx-auto rounded-full shadow-glow"></div>
                </div>
              </div>
              <div className="absolute -bottom-8 right-0 z-10 transform -rotate-3 hover:rotate-0 transition-all duration-300">
                <div className="btn-gradient text-white px-5 py-3 rounded-lg shadow-accent flex items-center relative overflow-hidden group border border-white/20">
                  <TruckIcon className="mr-2 h-4 w-4" />
                  <span className="font-semibold text-sm">{t("hero.globalShipping", "Global Shipping Available")}</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Мобильный бейдж */}
          <motion.div 
            className="lg:hidden flex justify-center mt-3 sm:mt-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.6 }}
          >
            <div className="btn-gradient text-white px-4 py-2 rounded-lg shadow-accent flex items-center justify-center relative overflow-hidden group border border-white/20 max-w-[260px]">
              <TruckIcon className="mr-2 h-4 w-4" />
              <span className="font-medium text-xs xs:text-sm">{t("hero.globalShipping", "Global Shipping Available")}</span>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="absolute bottom-0 left-0 w-full h-10 sm:h-16 bg-gradient-to-t from-background to-transparent z-10"></div>
    </section>
  );
}
