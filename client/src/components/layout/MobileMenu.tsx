import { <PERSON> } from "wouter";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "../ui/button";
import { cn } from "../../lib/utils";
import { LanguageSwitcher } from "../ui/language-switcher";
import { SearchBar } from "../ui/search-bar";
import { Mail, MapPin, Phone, PhoneCall, ChevronRight } from "lucide-react";
import { useEffect, useRef } from "react";

// Import shared product categories configuration
import { PRODUCT_CATEGORIES } from "../../constants/productCategories";

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  links: {
    href?: string;
    label: string;
    dropdown?: boolean;
    megaMenu?: boolean;
    items?: {
      href?: string;
      label: string;
      icon?: boolean;
      iconKey?: string;
      subcategories?: { href: string; label: string }[];
    }[];
  }[];
  activeLink: string;
}

// Use shared product categories for 100% consistency with desktop menu
const mobileProductCategories = PRODUCT_CATEGORIES;

const MobileMenu = ({ isOpen, onClose, links, activeLink }: MobileMenuProps) => {
  const { t } = useTranslation();
  const menuRef = useRef<HTMLDivElement>(null);
  const touchStartX = useRef<number | null>(null);
  
  useEffect(() => {
    // Listen for swipe gesture to close the menu
    const handleTouchStart = (e: TouchEvent) => {
      touchStartX.current = e.touches[0].clientX;
    };
    
    const handleTouchMove = (e: TouchEvent) => {
      if (!touchStartX.current) return;
      
      const touchX = e.touches[0].clientX;
      const diff = touchStartX.current - touchX;
      
      // If swiping left (positive diff) and enough distance moved
      if (diff > 40) {
        touchStartX.current = null;
        onClose();
      }
    };
    
    const handleTouchEnd = () => {
      touchStartX.current = null;
    };
    
    const menuElement = menuRef.current;
    if (menuElement) {
      menuElement.addEventListener('touchstart', handleTouchStart);
      menuElement.addEventListener('touchmove', handleTouchMove);
      menuElement.addEventListener('touchend', handleTouchEnd);
      
      return () => {
        menuElement.removeEventListener('touchstart', handleTouchStart);
        menuElement.removeEventListener('touchmove', handleTouchMove);
        menuElement.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [onClose]);
  
  // Lock body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);
  
  const isActive = (path: string | undefined) => {
    if (!path) return false;
    
    // Special case for home page
    if (path === "/" && activeLink === "/") return true;
    
    // Handle query parameter links for products filtering
    if (path.includes('?material=')) {
      // Check if we're on the products page
      const isProductsPage = activeLink.startsWith('/products');
      if (!isProductsPage) return false;
      
      // Extract material parameter from the link
      const linkMaterial = new URLSearchParams(path.split('?')[1]).get('material');
      
      // Extract material parameter from current URL if it exists
      const currentParams = activeLink.includes('?') 
        ? new URLSearchParams(activeLink.split('?')[1])
        : new URLSearchParams('');
      const currentMaterial = currentParams.get('material');
      
      // Match if materials are the same
      return linkMaterial === currentMaterial;
    }
    
    // For other pages, check if the current path starts with the link path
    return path !== "/" && activeLink.startsWith(path);
  };

  // Handle anchor links smoothly
  const handleAnchorClick = (e: React.MouseEvent, anchor: string) => {
    e.preventDefault();
    onClose(); // Close menu first
    
    setTimeout(() => {
      const element = document.getElementById(anchor);
      if (element) {
        window.scrollTo({
          top: element.offsetTop - 80, // Adjust for header height
          behavior: 'smooth'
        });
      }
    }, 300); // Delay to allow menu to close first
  };

  return (
    <div className="fixed inset-0 lg:hidden z-50 pointer-events-none">
      {/* Backdrop with solid white background */}
      <div 
        className={cn(
          "absolute inset-0 bg-white transition-opacity duration-300",
          isOpen ? "opacity-100 pointer-events-auto" : "opacity-0"
        )}
        onClick={onClose}
      />
      
      {/* Menu panel - optimized for mobile with reduced height */}
      <div
        id="mobile-menu"
        ref={menuRef}
        className={cn(
          "absolute top-[44px] xs:top-[48px] sm:top-[56px] md:top-[64px] right-0 w-full xs:w-11/12 sm:w-4/5 max-w-sm h-[65vh] xs:h-[70vh] sm:h-[75vh] md:h-[80vh] bg-white shadow-xl transition-transform duration-500 ease-out pointer-events-auto overflow-y-auto overscroll-contain momentum-scroll border-l border-t border-gray-200",
          isOpen ? "translate-x-0" : "translate-x-full"
        )}
        role="dialog"
        aria-modal="true"
        aria-label="Mobile navigation menu"
      >
        {/* Accent top border */}
        <div className="absolute top-0 left-0 w-full h-1 xs:h-1.5 bg-gradient-to-r from-primary via-accent to-primary shadow-accent"></div>
        
        {/* Swipe indicator */}
        <div className="absolute top-2 right-4 flex items-center gap-1 text-sm text-gray-500/70">
          <span className="text-xs">swipe</span>
          <ChevronRight className="h-4 w-4 animate-pulse" />
        </div>
        
        {/* Mobile handle/grip */}
        <div className="w-full flex justify-center py-1.5">
          <div className="w-12 h-1 bg-gray-200 rounded-full opacity-50"></div>
        </div>
        
        {/* Menu items - optimized spacing for mobile */}
        <div className="p-2 xs:p-3 sm:p-4 md:p-5 flex flex-col gap-1 xs:gap-2 md:gap-3">
          {links.map((link, index) =>
            link.megaMenu ? (
              <div key={`mega-menu-${index}`} className="relative">
                {/* Products Mega Menu Category Label */}
                <div className="relative font-inter font-medium py-2 xs:py-2.5 sm:py-3 cursor-pointer transition-all duration-300 group text-base tap-highlight rounded-lg pl-2 xs:pl-3 text-primary font-semibold">
                  <span className="block">{link.label}</span>
                  {/* Bottom border */}
                  <div className="absolute bottom-0 left-0 w-full h-px bg-neutral-100/50"></div>
                </div>

                {/* Mobile Products Categories */}
                <div className="pl-1 xs:pl-2 pt-1 bg-white rounded-md mt-1 mb-1">
                  {mobileProductCategories.map((category, subIndex) => {
                    const IconComponent = category.icon;

                    return (
                      <div key={category.id} className="mb-1">
                        {category.subcategories.length === 0 ? (
                          // Category without subcategories - direct link
                          <Link to={category.href} onClick={onClose}>
                            <div
                              className={cn(
                                "relative font-inter py-2 cursor-pointer transition-all duration-200 group text-sm xs:text-base tap-highlight rounded-lg flex items-center",
                                isActive(category.href)
                                  ? "text-accent font-medium pl-3 after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[1px] after:bg-accent"
                                  : "text-neutral-dark hover:text-accent hover:pl-3 active:bg-gray-50/30 hover:after:absolute hover:after:bottom-[-2px] hover:after:left-0 hover:after:w-full hover:after:h-[1px] hover:after:bg-accent/70"
                              )}
                              style={{ transitionDelay: `${(index + subIndex) * 20}ms` }}
                            >
                              {/* Active indicator bar */}
                              <div className={cn(
                                "absolute left-0 top-1/2 -translate-y-1/2 w-1.5 h-5 xs:h-6 rounded-sm bg-accent transition-opacity duration-300",
                                isActive(category.href) ? "opacity-100" : "opacity-0 group-hover:opacity-50"
                              )}></div>

                              {/* Icon */}
                              <IconComponent className="mr-3 h-4 w-4 text-primary transition-colors duration-200 group-hover:text-accent" />

                              <span className="block flex-grow">{category.name}</span>

                              {/* Bottom border */}
                              <div className="absolute bottom-0 left-0 w-full h-px bg-neutral-100/50"></div>
                            </div>
                          </Link>
                        ) : (
                          // Category with subcategories
                          <div>
                            <Link to={category.href} onClick={onClose}>
                              <div className="flex items-center py-2 pl-2 hover:bg-gray-50/50 rounded-md transition-colors duration-200">
                                <IconComponent className="mr-3 h-4 w-4 text-primary" />
                                <span className="text-sm font-semibold text-gray-800">{category.name}</span>
                              </div>
                            </Link>
                            <div className="ml-4 mb-1">
                              {category.subcategories.map((subItem) => (
                                <Link key={subItem.href} to={subItem.href} onClick={onClose}>
                                  <div className="py-1.5 px-2 cursor-pointer transition-all duration-200 hover:bg-gray-50/50 rounded-md">
                                    <span className="text-xs font-medium text-gray-600 hover:text-accent">{subItem.name}</span>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : link.dropdown ? (
              <div key={`dropdown-${index}`} className="relative">
                {/* Dropdown Category Label */}
                <div className="relative font-inter font-medium py-2 xs:py-2.5 sm:py-3 cursor-pointer transition-all duration-300 group text-base tap-highlight rounded-lg pl-2 xs:pl-3 text-primary font-semibold">
                  <span className="block">{link.label}</span>
                  {/* Bottom border */}
                  <div className="absolute bottom-0 left-0 w-full h-px bg-neutral-100/50"></div>
                </div>

                {/* Dropdown Items */}
                <div className="pl-1 xs:pl-2 pt-1 bg-white rounded-md mt-1 mb-1">
                  {link.items?.map((item, subIndex) => (
                    <Link key={item.href} to={item.href!} onClick={onClose}>
                      <div
                        className={cn(
                          "relative font-inter py-2 cursor-pointer transition-all duration-200 group text-sm xs:text-base tap-highlight rounded-lg",
                          isActive(item.href)
                            ? "text-accent font-medium pl-3 after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[1px] after:bg-accent"
                            : "text-neutral-dark hover:text-accent hover:pl-3 active:bg-gray-50/30 hover:after:absolute hover:after:bottom-[-2px] hover:after:left-0 hover:after:w-full hover:after:h-[1px] hover:after:bg-accent/70"
                        )}
                        style={{ transitionDelay: `${(index + subIndex) * 20}ms` }}
                      >
                        {/* Active indicator bar */}
                        <div className={cn(
                          "absolute left-0 top-1/2 -translate-y-1/2 w-1.5 h-5 xs:h-6 rounded-sm bg-accent transition-opacity duration-300",
                          isActive(item.href) ? "opacity-100" : "opacity-0 group-hover:opacity-50"
                        )}></div>

                        <span className="block flex-grow">{item.label}</span>

                        {/* Bottom border */}
                        <div className="absolute bottom-0 left-0 w-full h-px bg-neutral-100/50"></div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            ) : (
              <Link key={link.href || ''} to={link.href || ''} onClick={onClose}>
                <div
                  className={cn(
                    "relative font-inter font-medium py-2 xs:py-2.5 sm:py-3 cursor-pointer transition-all duration-300 group text-base tap-highlight rounded-lg",
                    isActive(link.href) 
                      ? "text-accent pl-2 xs:pl-3 bg-accent/5"
                      : "text-neutral-dark hover:text-accent hover:pl-2 xs:hover:pl-3 hover:bg-gray-50/50 active:bg-gray-100/50"
                  )}
                  style={{ transitionDelay: `${index * 50}ms` }}
                >
                  {/* Active indicator */}
                  <div className={cn(
                    "absolute left-0 top-1/2 -translate-y-1/2 w-1.5 h-5 xs:h-6 rounded-sm bg-accent transition-opacity duration-300",
                    isActive(link.href) ? "opacity-100" : "opacity-0 group-hover:opacity-50"
                  )}></div>
                  
                  <div className="flex justify-between items-center">
                    <span className="block">{link.label}</span>
                    {/* Removed chevron icon for clean menu design as required */}
                  </div>
                  
                  {/* Bottom border */}
                  <div className="absolute bottom-0 left-0 w-full h-px bg-neutral-100/50"></div>
                </div>
              </Link>
            )
          )}
          
          {/* Mobile-only Search Bar */}
          <div className="my-1 xs:my-2 sm:my-3 flex justify-start">
            <SearchBar
              placeholder={t("search.placeholder", "Search products...")}
              onSearchClick={onClose}
              iconOnly={true}
            />
          </div>

          {/* Language Switcher - already present in the header, may be redundant here */}
          <div className="mb-1 hidden xs:block">
            <LanguageSwitcher />
          </div>
          
          {/* Call to action buttons - compact for mobile */}
          <div className="mt-2 xs:mt-3 space-y-2 xs:space-y-3">
            {/* Get Price Button */}
            <Link to="/contact" onClick={onClose}>
              <Button
                className="bg-accent hover:bg-accent/90 text-white hover:shadow-md hover-scale w-full py-2.5 xs:py-3 sm:py-3.5 font-semibold rounded-lg text-sm xs:text-base active:translate-y-0.5 transition-transform active:shadow-inner"
              >
                <PhoneCall className="mr-2 h-4 w-4" />
                {t("header.requestQuote", "Request Quote")}
              </Button>
            </Link>

            {/* Request Callback Button */}
            <Link to="/contact" onClick={onClose}>
              <Button
                variant="secondary"
                className="w-full py-2 xs:py-2.5 sm:py-3 bg-primary/10 text-primary hover:bg-primary/20 text-sm xs:text-base active:translate-y-0.5 transition-transform active:bg-primary/30 tap-highlight"
              >
                {t("header.requestCallback", "Request Callback")}
              </Button>
            </Link>
          </div>
          
          {/* Contact info - compact for mobile */}
          <div className="mt-3 xs:mt-4 sm:mt-5 p-3 xs:p-4 rounded-lg bg-white text-neutral-700 shadow-sm border border-gray-200">
            <h4 className="font-semibold text-primary mb-2 text-sm xs:text-base">
              {t("contact.info.title", "Contact Information")}
            </h4>
            <div className="space-y-2 xs:space-y-3">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 p-1.5 -mx-1.5 rounded-md hover:bg-white/50 active:bg-white/80 transition-colors tap-highlight"
              >
                <div className="flex items-center justify-center h-6 w-6 bg-accent/10 rounded-full">
                  <Mail className="text-accent h-3 w-3" />
                </div>
                <p className="text-xs sm:text-sm"><EMAIL></p>
              </a>

              <a
                href="https://maps.google.com/?q=Tornimäe+5,+Tallinn,+10145,+Estonia"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-start gap-2 p-1.5 -mx-1.5 rounded-md hover:bg-white/50 active:bg-white/80 transition-colors tap-highlight"
              >
                <div className="flex items-center justify-center h-6 w-6 mt-0.5 bg-accent/10 rounded-full">
                  <MapPin className="text-accent h-3 w-3" />
                </div>
                <p className="text-xs sm:text-sm">Tallinn, Estonia</p>
              </a>

              <a
                href="tel:+37255589800"
                className="flex items-center gap-2 p-1.5 -mx-1.5 rounded-md hover:bg-white/50 active:bg-white/80 transition-colors tap-highlight"
              >
                <div className="flex items-center justify-center h-6 w-6 bg-accent/10 rounded-full">
                  <Phone className="text-accent h-3 w-3" />
                </div>
                <p className="text-xs sm:text-sm">+372 55589800</p>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileMenu;
