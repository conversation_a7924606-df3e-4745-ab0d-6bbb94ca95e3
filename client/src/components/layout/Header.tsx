import { useState, useEffect, useRef } from "react";
import { Link, useLocation } from "wouter";
import { useTranslation } from "react-i18next";
import { Logo } from "../ui/logo";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON><PERSON>, PhoneCall } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { LanguageSwitcher } from "../ui/language-switcher";
import MobileMenu from "./MobileMenu";
import { SearchBar } from "../ui/search-bar";

import { cn } from "../../lib/utils";

// Import shared product categories configuration
import { PRODUCT_CATEGORIES } from "../../constants/productCategories";

// Use shared product categories for consistency with mobile menu
const productCategories = PRODUCT_CATEGORIES;

// Mega menu component for Products dropdown
const ProductsMegaMenu = ({ isOpen, onMouseEnter, onMouseLeave }: {
  isOpen: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}) => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [focusedCategoryIndex, setFocusedCategoryIndex] = useState<number>(-1);
  const [focusedSubcategoryIndex, setFocusedSubcategoryIndex] = useState<number>(-1);
  const categoryRefs = useRef<(HTMLAnchorElement | null)[]>([]);
  const subcategoryRefs = useRef<(HTMLAnchorElement | null)[]>([]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          onMouseLeave();
          break;
        case 'ArrowDown':
          event.preventDefault();
          if (focusedCategoryIndex < productCategories.length - 1) {
            const newIndex = focusedCategoryIndex + 1;
            setFocusedCategoryIndex(newIndex);
            setHoveredCategory(productCategories[newIndex].id);
            categoryRefs.current[newIndex]?.focus();
          }
          break;
        case 'ArrowUp':
          event.preventDefault();
          if (focusedCategoryIndex > 0) {
            const newIndex = focusedCategoryIndex - 1;
            setFocusedCategoryIndex(newIndex);
            setHoveredCategory(productCategories[newIndex].id);
            categoryRefs.current[newIndex]?.focus();
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (hoveredCategory && focusedCategoryIndex >= 0) {
            const category = productCategories[focusedCategoryIndex];
            if (category.subcategories.length > 0) {
              setFocusedSubcategoryIndex(0);
              subcategoryRefs.current[0]?.focus();
            }
          }
          break;
        case 'ArrowLeft':
          event.preventDefault();
          if (focusedSubcategoryIndex >= 0) {
            setFocusedSubcategoryIndex(-1);
            categoryRefs.current[focusedCategoryIndex]?.focus();
          }
          break;
        case 'Enter':
          event.preventDefault();
          if (focusedSubcategoryIndex >= 0) {
            subcategoryRefs.current[focusedSubcategoryIndex]?.click();
          } else if (focusedCategoryIndex >= 0) {
            // Navigate to category page
            const category = productCategories[focusedCategoryIndex];
            window.location.href = category.href;
          }
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onMouseLeave, hoveredCategory, focusedCategoryIndex, focusedSubcategoryIndex]);

  // Handle click outside to close menu
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node) && isOpen) {
        onMouseLeave();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onMouseLeave]);

  // Reset focus states when menu closes
  useEffect(() => {
    if (!isOpen) {
      setFocusedCategoryIndex(-1);
      setFocusedSubcategoryIndex(-1);
      setHoveredCategory(null);
    }
  }, [isOpen]);

  return (
    <div
      ref={menuRef}
      className="relative"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div
        className="relative group"
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-controls="products-mega-menu"
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            if (isOpen) {
              onMouseLeave();
            } else {
              onMouseEnter();
            }
          }
        }}
      >
        <div className="font-inter font-medium transition-all duration-300 cursor-pointer hover:text-[#40BFB9]">
          Products
        </div>
        {/* Single gradient underline effect */}
        <div className="absolute -bottom-[1px] left-0 h-[2px] bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] transition-all duration-300 transform origin-left w-0 scale-x-0 group-hover:w-full group-hover:scale-x-100"></div>
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            id="products-mega-menu"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-0.5 z-50 bg-white border border-gray-100 rounded-lg shadow-xl overflow-hidden"
            style={{ minWidth: '480px' }}
            role="menu"
            aria-label="Products mega menu"
          >
            <div className="flex">
              {/* Left Column - Categories */}
              <div className="w-1/2 p-2 border-r border-gray-100" role="menubar">
                {productCategories && productCategories.length > 0 ? productCategories.map((category, index) => {
                  if (!category || !category.icon) {
                    console.warn('Invalid category:', category);
                    return null;
                  }

                  const IconComponent = category.icon;
                  const isHovered = hoveredCategory === category.id;
                  const isFocused = focusedCategoryIndex === index;

                  return (
                    <Link
                      key={category.id}
                      to={category.href}
                      ref={(el) => (categoryRefs.current[index] = el)}
                      onMouseEnter={() => {
                        setHoveredCategory(category.id);
                        setFocusedCategoryIndex(index);
                      }}
                      onMouseLeave={() => {
                        // Don't clear hover state immediately to prevent flickering
                      }}
                      onClick={() => {
                        // Close menu when clicking a category
                        setTimeout(() => onMouseLeave(), 100);
                      }}
                      className="group/category block"
                      role="menuitem"
                      tabIndex={0}
                      aria-haspopup="true"
                      aria-expanded={isHovered}
                    >
                      <div className={cn(
                        "relative flex items-center px-2 py-1.5 cursor-pointer transition-all duration-300 rounded-lg",
                        isHovered || isFocused
                          ? "bg-gradient-to-r from-[#2D7EB6]/5 to-[#40BFB9]/5 border-l-3 border-[#2D7EB6]"
                          : "hover:bg-gradient-to-r hover:from-[#2D7EB6]/5 hover:to-[#40BFB9]/5 hover:border-l-3 hover:border-[#2D7EB6]/50"
                      )}>
                        {/* Compact gradient underline effect */}
                        <div className={cn(
                          "absolute -bottom-[1px] left-2 right-2 h-[2px] bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] transition-all duration-300 transform origin-left",
                          isHovered || isFocused ? "scale-x-100" : "scale-x-0"
                        )}></div>

                        {IconComponent && (
                          <IconComponent className={cn(
                            "mr-3 h-5 w-5 transition-all duration-300",
                            isHovered || isFocused
                              ? "text-[#40BFB9] scale-110"
                              : "text-[#2D7EB6] group-hover/category:text-[#40BFB9] group-hover/category:scale-105"
                          )} />
                        )}

                        <span className={cn(
                          "text-sm font-medium transition-colors duration-300 whitespace-nowrap",
                          isHovered || isFocused
                            ? "text-[#2D7EB6]"
                            : "text-gray-700 group-hover/category:text-[#2D7EB6]"
                        )}>
                          {category.name || 'Unknown Category'}
                        </span>
                      </div>
                    </Link>
                  );
                }).filter(Boolean) : (
                  <div className="text-gray-500 text-sm py-4">
                    No categories available
                  </div>
                )}
              </div>

              {/* Right Column - Subcategories */}
              <div className="w-1/2 p-2" role="menu">
                <AnimatePresence mode="wait">
                  {hoveredCategory && (
                    <motion.div
                      key={hoveredCategory}
                      initial={{ opacity: 0, x: 10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.15 }}
                    >
                      {(() => {
                        try {
                          const category = productCategories.find(cat => cat && cat.id === hoveredCategory);
                          if (!category || !category.subcategories || category.subcategories.length === 0) {
                            return (
                              <div className="text-gray-500 text-sm italic py-4">
                                No subcategories available
                              </div>
                            );
                          }

                          return (
                            <div>
                              <h4 className="text-sm font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-100">
                                {category.name || 'Category'} Products
                              </h4>
                              <div className="space-y-0.5" role="group">
                                {category.subcategories.filter(sub => sub && sub.href && sub.name).map((subcategory, subIndex) => (
                                  <Link
                                    key={subcategory.href}
                                    to={subcategory.href}
                                    ref={(el) => (subcategoryRefs.current[subIndex] = el)}
                                    className="group/sub block"
                                    role="menuitem"
                                    tabIndex={focusedSubcategoryIndex === subIndex ? 0 : -1}
                                    onFocus={() => setFocusedSubcategoryIndex(subIndex)}
                                    onMouseEnter={() => setFocusedSubcategoryIndex(subIndex)}
                                    onClick={() => {
                                      // Close menu when clicking a subcategory
                                      setTimeout(() => onMouseLeave(), 100);
                                    }}
                                  >
                                    <div className={cn(
                                      "relative cursor-pointer transition-all duration-300 rounded-md group",
                                      // Adjust padding based on whether it's an "All [Category] Products" link
                                      subcategory.name.startsWith('All ') ? "px-2 py-0.5" : "px-2 py-1",
                                      focusedSubcategoryIndex === subIndex
                                        ? "bg-gradient-to-r from-[#2D7EB6]/5 to-[#40BFB9]/5 border-l-2 border-[#2D7EB6]"
                                        : "hover:bg-gradient-to-r hover:from-[#2D7EB6]/5 hover:to-[#40BFB9]/5"
                                    )}>
                                      <span className={cn(
                                        "text-sm font-medium transition-colors duration-300 whitespace-nowrap flex items-center",
                                        // Visual distinction for "All [Category] Products" links
                                        subcategory.name.startsWith('All ')
                                          ? "text-[#2D7EB6] italic"
                                          : "text-gray-600",
                                        focusedSubcategoryIndex === subIndex
                                          ? "text-[#2D7EB6] font-semibold"
                                          : subcategory.name.startsWith('All ')
                                            ? "group-hover/sub:text-[#2D7EB6]"
                                            : "group-hover/sub:text-[#40BFB9]"
                                      )}>
                                        {/* Add arrow icon for "All [Category] Products" links */}
                                        {subcategory.name.startsWith('All ') && (
                                          <span className="mr-1.5 text-xs">→</span>
                                        )}
                                        {subcategory.name}
                                      </span>
                                      {/* Standardized gradient underline effect */}
                                      <div className={cn(
                                        "absolute -bottom-[1px] left-2 right-2 h-[2px] bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] transition-all duration-300 transform origin-left",
                                        focusedSubcategoryIndex === subIndex ? "scale-x-100" : "scale-x-0 group-hover:scale-x-100"
                                      )}></div>
                                    </div>
                                  </Link>
                                ))}
                              </div>
                            </div>
                          );
                        } catch (error) {
                          console.error('Error rendering subcategories:', error);
                          return (
                            <div className="text-red-500 text-sm py-4">
                              Error loading subcategories
                            </div>
                          );
                        }
                      })()}
                    </motion.div>
                  )}
                </AnimatePresence>

                {!hoveredCategory && (
                  <div className="text-gray-500 text-sm py-4">
                    Hover over a category to see products
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Custom desktop dropdown component for non-Products dropdowns
const DesktopNavDropdown = ({ link }: { link: any }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 200);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={dropdownRef}
      className="relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        className="relative group"
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-controls={`dropdown-${link.label.toLowerCase().replace(/\s+/g, '-')}`}
      >
        <div className="font-inter font-medium transition-all duration-300 cursor-pointer hover:text-[#40BFB9]">
          {link.label}
        </div>
        <div className="absolute -bottom-[1px] left-0 h-[2px] bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] transition-all duration-300 transform origin-left w-0 scale-x-0 group-hover:w-full group-hover:scale-x-100"></div>
      </div>

      {isOpen && (
        <div
          id={`dropdown-${link.label.toLowerCase().replace(/\s+/g, '-')}`}
          className="absolute top-full left-1/2 transform -translate-x-1/2 mt-0.5 z-50 animate-in fade-in-50 duration-200 min-w-[160px] bg-white border border-gray-100 rounded-lg shadow-xl overflow-hidden"
          role="menu"
          aria-label={`${link.label} submenu`}
        >
          <div className="py-0.5" role="none">
            {link.items?.map((item: any) => (
              <Link
                key={item.href}
                to={item.href}
                className="block focus:outline-none focus:ring-2 focus:ring-[#2D7EB6]/50 focus:ring-offset-2 rounded-md"
                role="menuitem"
                tabIndex={0}
                aria-label={`Navigate to ${item.label}`}
              >
                <div
                  className="relative px-2.5 py-1.5 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-[#2D7EB6]/5 hover:to-[#40BFB9]/5 focus:bg-gradient-to-r focus:from-[#2D7EB6]/5 focus:to-[#40BFB9]/5 group"
                  onClick={() => {
                    // Close dropdown when clicking a submenu item
                    setTimeout(() => {
                      setIsOpen(false);
                    }, 100);
                  }}
                >
                  <span className="text-sm font-medium text-gray-700 transition-colors duration-300 group-hover:text-[#2D7EB6] group-focus:text-[#2D7EB6] whitespace-nowrap">
                    {item.label}
                  </span>
                  {/* Compact underline effect */}
                  <div className="absolute -bottom-[1px] left-2.5 right-2.5 h-[2px] bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] transform scale-x-0 group-hover:scale-x-100 group-focus:scale-x-100 transition-transform duration-300 origin-left"></div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [location] = useLocation();
  const { t } = useTranslation();

  // Track scroll position to apply different styles
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  
  // Fix for dropdown hover blocking page scrolling
  useEffect(() => {
    // Ensure body overflow is never hidden when dropdown menus are hovered
    document.body.style.overflow = '';
    
    return () => {
      // Cleanup in case component unmounts
      document.body.style.overflow = '';
    };
  }, []);

  const toggleMenu = () => setIsOpen(!isOpen);
  const closeMenu = () => setIsOpen(false);

  // State for Products mega menu with improved hover stability
  const [isProductsMenuOpen, setIsProductsMenuOpen] = useState(false);
  const productsMenuTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleProductsMenuEnter = () => {
    if (productsMenuTimeoutRef.current) {
      clearTimeout(productsMenuTimeoutRef.current);
      productsMenuTimeoutRef.current = null;
    }
    setIsProductsMenuOpen(true);
  };

  const handleProductsMenuLeave = () => {
    // Increased delay for better stability when moving between columns
    productsMenuTimeoutRef.current = setTimeout(() => {
      setIsProductsMenuOpen(false);
    }, 300);
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (productsMenuTimeoutRef.current) {
        clearTimeout(productsMenuTimeoutRef.current);
      }
    };
  }, []);

  const navLinks = [
    { href: "/", label: t("header.home", "Home") },
    {
      megaMenu: true,
      label: t("header.products", "Products")
    },
    { href: "/services", label: t("header.services", "Services") },
    {
      dropdown: true,
      label: t("header.company", "Company"),
      items: [
        { href: "/about", label: t("header.about", "About Us") },
        { href: "/projects", label: t("header.projects", "Projects") },
        { href: "/careers", label: t("header.careers", "Careers") }
      ]
    },
    { href: "/contact", label: t("header.contact", "Contact") }
  ];

  const isActive = (path: string | undefined) => {
    if (!path) return false;
    // Special case for home page
    if (path === "/" && location === "/") return true;
    // For other pages, check if the current path starts with the link path
    return path !== "/" && location.startsWith(path);
  };

  // Handle anchor links smoothly
  const handleAnchorClick = (e: React.MouseEvent<HTMLAnchorElement>, anchor: string) => {
    e.preventDefault();
    const element = document.getElementById(anchor);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80, // Adjust for header height
        behavior: 'smooth'
      });
    }
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 bg-white shadow-sm ${
      scrolled ? 'py-2 sm:py-3 shadow-lg' : 'py-3 sm:py-4'
    }`}>
    <div className="absolute inset-0 bg-white border-b border-gray-200"></div>
    <div className="container relative z-10 mx-auto px-2 sm:px-4 md:px-6 flex justify-between items-center">
      <Link to="/">
        <div className="flex items-center cursor-pointer hover-scale transition-transform duration-300">
          <Logo className="transition-all duration-300" />
        </div>
      </Link>

      {/* Desktop Navigation */}
      <nav className="hidden lg:flex items-center gap-5 xl:gap-7">
        {navLinks.map((link, idx) =>
          link.megaMenu ? (
            // Use mega menu for Products
            <ProductsMegaMenu
              key={`mega-menu-${idx}`}
              isOpen={isProductsMenuOpen}
              onMouseEnter={handleProductsMenuEnter}
              onMouseLeave={handleProductsMenuLeave}
            />
          ) : link.dropdown ? (
            // Use our custom hover dropdown for desktop menu items
            <DesktopNavDropdown key={`desktop-dropdown-${idx}`} link={link} />
          ) : (
            <Link key={`nav-link-${idx}`} to={link.href || ""}>
              <div className="relative group">
                <div className={`font-inter font-medium transition-all duration-300 cursor-pointer ${
                  isActive(link.href) ? 'text-[#40BFB9] font-bold' : 'hover:text-[#40BFB9]'
                }`}>
                  {link.label}
                </div>
                {/* Single gradient underline effect */}
                <div className={`absolute -bottom-[1px] left-0 h-[2px] bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] transition-all duration-300 transform origin-left ${
                  isActive(link.href) ? 'w-full scale-x-100' : 'w-0 scale-x-0 group-hover:w-full group-hover:scale-x-100'
                }`}></div>
              </div>
            </Link>
          )
        )}

        {/* Search Icon */}
        <div className="w-auto">
          <SearchBar
            placeholder={t("search.placeholder", "Search products...")}
            iconOnly={true}
          />
        </div>

        {/* Language Switcher */}
        <div className="mx-1">
          <LanguageSwitcher />
        </div>

        {/* Get Price Button */}
        <Link to="/contact">
          <Button
            className="btn-gradient text-white hover-lift hover-glow font-semibold ml-2 px-3 lg:px-4 py-2 shadow-accent border border-white/20 whitespace-nowrap"
          >
            <PhoneCall className="mr-1 h-4 w-4" /> {t("header.requestQuote", "Request Quote")}
          </Button>
        </Link>
      </nav>

      {/* Mobile Menu Toggle & Mobile Actions */}
      <div className="lg:hidden flex items-center gap-1 xs:gap-1.5 sm:gap-2">
        {/* Mobile Search - tablet only */}
        <div className="hidden xs:block sm:block">
          <SearchBar
            placeholder={t("search.placeholder", "Search products...")}
            iconOnly={true}
          />
        </div>

        {/* Mobile Language Switcher - xs and up */}
        <div className="block">
          <LanguageSwitcher />
        </div>

        {/* Mobile Call Button */}
        <Link to="/contact">
          <Button
            size="sm"
            className="btn-gradient hover-lift hover-glow text-white px-1.5 xs:px-2 sm:px-3 py-1 xs:py-1.5 shadow-accent border border-white/20 text-xs sm:text-sm font-medium"
            aria-label={t("header.requestCallback", "Request Callback")}
          >
            <PhoneCall className="h-3 xs:h-3.5 sm:h-4 w-3 xs:w-3.5 sm:w-4" />
            <span className="ml-1 hidden xs:inline whitespace-nowrap">{t("header.requestCallback", "Request Callback")}</span>
          </Button>
        </Link>

        {/* Menu Toggle Button */}
        <Button
          variant="ghost"
          size="icon"
          className="neumorph-btn text-primary hover:text-accent p-1 xs:p-1.5 sm:p-2 rounded-full hover-lift hover-glow shadow-md border border-white/30"
          onClick={toggleMenu}
          aria-label="Toggle menu"
          aria-expanded={isOpen}
          aria-controls="mobile-menu"
        >
          <Menu className="h-3.5 xs:h-4 sm:h-5 w-3.5 xs:w-4 sm:w-5" />
        </Button>
      </div>
    </div>

    {/* Mobile Menu */}
    <MobileMenu
      isOpen={isOpen}
      onClose={closeMenu}
      links={navLinks}
      activeLink={location}
    />
  </header>
  );
};

export default Header;
