// Shared product categories configuration for both desktop and mobile menus
// This ensures 100% consistency between navigation experiences

import { AluminumProfilesIcon, PolyethyleneIcon, SteelIcon, CastIronIcon, PipeIcon } from "../components/icons";
import { Droplets, Building2 } from "lucide-react";

export interface ProductSubcategory {
  name: string;
  href: string;
}

export interface ProductCategory {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  href: string;
  subcategories: ProductSubcategory[];
}

// SINGLE SOURCE OF TRUTH: Product categories used by both desktop and mobile menus
export const PRODUCT_CATEGORIES: ProductCategory[] = [
  {
    id: 'aluminum-profiles',
    name: 'Aluminum Profiles',
    icon: AluminumProfilesIcon,
    href: '/products?material=aluminum',
    subcategories: [
      { name: 'Aluminum U-Profiles', href: '/products/aluminum-u-profiles' },
      { name: 'Aluminum T-Profiles', href: '/products/aluminum-t-profiles' },
      { name: 'Aluminum Special Profiles', href: '/products/aluminum-special-profiles' },
      { name: 'LED Aluminum Profiles', href: '/products/led-profiles' },
      { name: 'All Aluminum Products', href: '/products?material=aluminum' }
    ]
  },
  {
    id: 'polyethylene',
    name: 'Polyethylene',
    icon: PolyethyleneIcon,
    href: '/products?material=polyethylene',
    subcategories: [
      { name: 'HDPE Pipes', href: '/products/hdpe-pipes' },
      { name: 'Double-Wall Corrugated Pipes', href: '/products/double-corrugated-pipes' },
      { name: 'All Polyethylene Products', href: '/products?material=polyethylene' }
    ]
  },
  {
    id: 'steel-products',
    name: 'Steel Products',
    icon: SteelIcon,
    href: '/products?material=steel',
    subcategories: [
      { name: 'HSAW Steel Pipes', href: '/products/hsaw-pipes' },
      { name: 'Oil & Gas Steel Pipes', href: '/products/oil-gas-pipes' },
      { name: 'HSAW Pile Pipes', href: '/products/pile-pipes' },
      { name: 'All Steel Products', href: '/products?material=steel' }
    ]
  },
  {
    id: 'cast-iron-products',
    name: 'Cast Iron Products',
    icon: CastIronIcon,
    href: '/products?material=cast-iron',
    subcategories: [
      { name: 'Cast Iron Manhole Covers', href: '/products/manhole-covers' },
      { name: 'Cast Iron Drainage Grates', href: '/products/drainage-grates' },
      { name: 'All Cast Iron Products', href: '/products?material=cast-iron' }
    ]
  },
  {
    id: 'fittings-components',
    name: 'Fittings & Components',
    icon: PipeIcon,
    href: '/products?material=fittings',
    subcategories: [
      { name: 'Elbow 90° Fittings', href: '/products/elbow-90-fitting' },
      { name: 'Equal TEE 90° Fittings', href: '/products/equal-tee-90-fitting' },
      { name: 'Dismantling Pieces', href: '/products/dismantling-pieces' },
      { name: 'All Fittings & Components', href: '/products?material=fittings' }
    ]
  },
  {
    id: 'drainage-systems',
    name: 'Drainage Systems',
    icon: Droplets,
    href: '/products?material=drainage',
    subcategories: [
      { name: 'Rainwater Grill D400', href: '/products/rainwater-grill-d400' },
      { name: 'All Drainage Systems', href: '/products?material=drainage' }
    ]
  },
  {
    id: 'urban-infrastructure',
    name: 'Urban Infrastructure',
    icon: Building2,
    href: '/products?material=urban-infrastructure',
    subcategories: [
      { name: 'Gate Valves', href: '/products/gate-valve' },
      { name: 'Butterfly Valves', href: '/products/butterfly-valve' },
      { name: 'Ground Fire Hydrant', href: '/products/ground-fire-hydrant' },
      { name: 'Park Seats', href: '/products/park-seat' },
      { name: 'NATO Security Posts', href: '/products/nato-post' },
      { name: 'Cast Iron Barriers', href: '/products/cast-iron-barriers' },
      { name: 'All Urban Infrastructure', href: '/products?material=urban-infrastructure' }
    ]
  }
];

// Export for backward compatibility and easier imports
export default PRODUCT_CATEGORIES;
