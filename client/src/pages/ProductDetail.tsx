import { useEffect, useRef, useState } from "react";
import { useLocation, useRoute } from "wouter";
import { ArrowLeft, Check, FileDown, Loader2 } from "lucide-react";
import { Button } from "../components/ui/button";
import { useTranslation } from "react-i18next";
import { QuoteRequestModal } from "../components/ui/quote-request-modal";
import { CtaSection } from "../components/sections/CtaSection";


import { BreadcrumbSchema } from "../components/seo/Sitemap";
import MetaTags from "../components/seo/MetaTags";
import SchemaOrg from "../components/seo/SchemaOrg";
import { isMobileDevice } from "../utils/mobile-detection";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "../lib/queryClient"; // Import apiRequest

// Типизация документов продукта
interface ProductDocument {
  id: number;
  title: string;
  description?: string;
  fileUrl: string;
}

// Главный компонент ProductDetail
const ProductDetail = () => {
  const { t, i18n } = useTranslation();
  const [, params] = useRoute("/products/:slug");
  const [, navigate] = useLocation();
  const detailsRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  // Получаем slug из роут-параметра
  const slug = params?.slug ?? "";

  // MOBILE FIX: Track previous slug to detect navigation changes
  const [previousSlug, setPreviousSlug] = useState<string>("");
  const [isMobile, setIsMobile] = useState<boolean>(false);

  // Detect mobile device on mount and window resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(isMobileDevice());
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // ENHANCED FETCH: Optimized for mobile navigation transitions
  const {
    data: product,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["product", slug, i18n.language],
    queryFn: async () => {
      if (!slug) return null;

      // Enhanced debug logging for mobile navigation
      if (import.meta.env.DEV) {
        console.log('🔍 ProductDetail fetching:', {
          slug,
          language: i18n.language,
          endpoint: `/api/products/slug/${slug}?language=${i18n.language}`,
          timestamp: new Date().toISOString()
        });
      }

      try {
        const res = await apiRequest(
          "GET",
          `/api/products/slug/${slug}?language=${i18n.language}`
        );

        if (import.meta.env.DEV) {
          console.log('✅ API response received for slug:', slug);
        }

        const data = await res.json();

        if (import.meta.env.DEV) {
          console.log('📦 API response data:', {
            productTitle: data?.title,
            productId: data?.id,
            slug: data?.slug || data?.productId
          });
        }

        return data;
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error('❌ API request error for slug:', slug, error);
        }
        throw error; // Let React Query handle the error
      }
    },
    enabled: !!slug,
    staleTime: 0, // Always fetch fresh data for mobile navigation
    refetchOnWindowFocus: false, // Disable to prevent unnecessary refetches
    refetchOnMount: true, // Always refetch when component mounts
    retry: (failureCount, error) => {
      // Enhanced retry logic for mobile devices
      if (failureCount >= 2) return false;

      // Don't retry on 404s (product not found)
      if (error && typeof error === 'object' && 'message' in error) {
        if (error.message.includes('404')) return false;
      }

      return true;
    },
    retryDelay: 1000, // 1 second delay between retries
    // CRITICAL: Force network requests to bypass any caching issues
    networkMode: 'always',
  });

  if (import.meta.env.DEV) {
    console.log('ProductDetail debug', {
      slug,
      product: product ? {
        title: product.title,
        id: product.id,
        slug: product.slug || product.productId
      } : null,
      isLoading,
      isError,
      isMobile
    });
  }

  // MOBILE FIX: Prevent showing stale product data during navigation
  const isProductMismatch = product && (
    (product.slug && product.slug !== slug) ||
    (product.productId && product.productId !== slug)
  );

  if (isProductMismatch && isMobile) {
    if (import.meta.env.DEV) {
      console.log('⚠️ ProductDetail: Product mismatch detected on mobile, showing loader', {
        expectedSlug: slug,
        productSlug: product.slug || product.productId,
        productTitle: product.title
      });
    }

    // Show loading state instead of mismatched product data
    return (
      <div className="container mx-auto px-4 py-16 flex flex-col items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin mb-4" />
        <p>{t("productDetail.loading", "Loading product information...")}</p>
      </div>
    );
  }


  // Mobile layout fixes - CRITICAL FIX: Exclude logos from global image styling
  useEffect(() => {
    if (typeof window !== "undefined") window.scrollTo(0, 0);

    const fixMobileLayout = () => {
      if (isMobileDevice() && detailsRef.current) {
        try {
          // MOBILE LOGO BUG FIX: Only target product content images, NOT header/footer logos
          const productSection = document.querySelector('section[class*="py-12"]');
          if (productSection) {
            // Target only images within the product content area, exclude logos
            productSection.querySelectorAll("img").forEach((img) => {
              if (img instanceof HTMLImageElement) {
                // Additional safety check: exclude images in header/footer and logos
                const isInHeaderOrFooter = img.closest('header') || img.closest('footer');
                const isLogo = img.alt?.toLowerCase().includes('logo') ||
                              img.src?.includes('logo') ||
                              img.hasAttribute('data-logo') ||
                              img.getAttribute('aria-label')?.toLowerCase().includes('logo');

                if (!isInHeaderOrFooter && !isLogo) {
                  img.style.maxWidth = "100%";
                  img.style.height = "auto";
                  if (window.getComputedStyle(img).position === "absolute") {
                    img.style.position = "relative";
                  }
                }
              }
            });

            // Table fixes remain scoped to product content area
            productSection.querySelectorAll("table").forEach((table) => {
              table.style.maxWidth = "100%";
              table.style.width = "100%";
              table.style.tableLayout = "fixed";
              const parent = table.parentElement;
              if (parent && parent.clientWidth < table.scrollWidth) {
                parent.style.overflowX = "auto";
              }
            });
          }
        } catch {}
      }
    };

    fixMobileLayout();
    setTimeout(fixMobileLayout, 500);

    // Cleanup function to reset any accidentally modified logos
    return () => {
      try {
        document.querySelectorAll("img[alt*='logo' i], img[aria-label*='logo' i]").forEach((img) => {
          if (img instanceof HTMLImageElement) {
            // Remove any inline styles that might have been applied to logos
            img.style.removeProperty('max-width');
            img.style.removeProperty('height');
            img.style.removeProperty('width');
          }
        });
      } catch {}
    };
  }, []);

  // CRITICAL FIX: Invalidate cache when slug or language changes
  // This ensures proper data refetching during mobile navigation transitions
  useEffect(() => {
    if (slug) {
      // Use the EXACT same queryKey pattern as the useQuery above
      queryClient.invalidateQueries({
        queryKey: ["product", slug, i18n.language],
        exact: true
      });

      // Also invalidate any partial matches to ensure clean state
      queryClient.invalidateQueries({
        queryKey: ["product", slug],
        exact: false
      });

      if (import.meta.env.DEV) {
        console.log('🔄 ProductDetail: Cache invalidated for slug change', {
          slug,
          language: i18n.language
        });
      }
    }
  }, [slug, i18n.language, queryClient]);

  // MOBILE FIX: Enhanced navigation handling for mobile devices
  useEffect(() => {
    if (slug && slug !== previousSlug) {
      if (import.meta.env.DEV) {
        console.log('🔄 ProductDetail: Slug changed', {
          from: previousSlug,
          to: slug,
          isMobile,
          language: i18n.language
        });
      }

      // Update previous slug tracker
      setPreviousSlug(slug);

      // For mobile devices, be more aggressive about cache clearing and refetching
      if (isMobile) {
        // Clear any existing queries for the old product
        if (previousSlug) {
          queryClient.removeQueries({
            queryKey: ["product", previousSlug],
            exact: false
          });
        }

        // Force immediate refetch for the new product
        queryClient.refetchQueries({
          queryKey: ["product", slug, i18n.language],
          exact: true
        });

        if (import.meta.env.DEV) {
          console.log('📱 ProductDetail: Mobile navigation - cleared cache and forced refetch', {
            slug,
            language: i18n.language
          });
        }
      } else {
        // For desktop, use standard refetch
        queryClient.refetchQueries({
          queryKey: ["product", slug, i18n.language],
          exact: true
        });
      }
    }
  }, [slug, previousSlug, isMobile, i18n.language, queryClient]);

  // Лоадер
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16 flex flex-col items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin mb-4" />
        <p>{t("productDetail.loading", "Loading product information...")}</p>
      </div>
    );
  }

  // Ошибка
  if (isError) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h2 className="text-xl font-semibold mb-2">
          {t("productDetail.error", "Failed to load product")}
        </h2>
        <p className="mb-4">{t("productDetail.errorMessage", "An error occurred while fetching the product details.")}</p>
      </div>
    );
  }

  // Ошибка/нет продукта
  if (!product) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h2 className="text-xl font-semibold mb-2">
          {t("productDetail.notFound", "Product Not Found")}
        </h2>
        <p className="mb-4">
          {t("productDetail.notFoundMessage", "The requested product could not be found.")}
        </p>
        <Button onClick={() => navigate("/products")}>
          {t("products.backToProducts", "Back to Products")}
        </Button>
      </div>
    );
  }

  // Отображение продукта
  return (
    <>
      <MetaTags
        title={`${product.title} - ${t("products.metaTitle", "MetaNord Products")}`}
        description={product.description?.substring(0, 150) + "..."}
        keywords={`${product.category}, industrial products, MetaNord, aluminum profiles, infrastructure solutions`}
        ogImage={product.image}
        ogType="product"
        canonical={`https://metanord.eu/products/${product.productId}`}
        ogUrl={`https://metanord.eu/products/${product.productId}`}
      />

      {/* SEO-схемы */}
      <SchemaOrg
        type="product"
        title={product.title}
        description={product.description}
        imageUrl={product.image}
        product={{
          name: product.title,
          description: product.description,
          image: product.image,
          category: product.category,
          brand: "MetaNord",
          availability: "https://schema.org/InStock",
        }}
        breadcrumbs={[
          { name: t("navigation.home", "Home"), url: "/" },
          { name: t("navigation.products", "Products"), url: "/products" },
          { name: product.title, url: `/products/${product.productId}` },
        ]}
      />

      <BreadcrumbSchema
        items={[
          { name: t("home", "Home"), url: "/", position: 1 },
          { name: t("products.title", "Products"), url: "/products", position: 2 },
          { name: product.title, url: `/products/${product.productId}`, position: 3 },
        ]}
      />

      {/* Hero Section with Wave Background - Optimized for mobile visibility */}
      <section className="relative bg-gradient-to-r from-[#2D7EB6] to-[#40BFB9] text-white min-h-[220px] sm:min-h-[280px] md:min-h-[300px] overflow-hidden mt-[44px] xs:mt-[48px] sm:mt-[56px] md:mt-[64px]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10 relative h-full flex items-center py-8 sm:py-12 md:py-16">
          <div ref={detailsRef} className="text-left w-full">
            <button
              onClick={() => navigate("/products")}
              className="inline-flex items-center text-white/80 hover:text-white mb-4 sm:mb-6 md:mb-8 transition-all duration-300 hover:scale-105"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("products.backToProducts", "Back to Products")}
            </button>
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-semibold text-white mb-3 sm:mb-4 md:mb-6 max-w-4xl">
              {product.title}
            </h1>
            <p
              className="text-lg md:text-xl font-medium text-white/90 max-w-2xl"
              style={{
                textShadow: "0 2px 16px #2D7EB6aa, 0 1px 3px #40BFB966",
              }}
            >
              {(() => {
                const category = product.category?.toLowerCase();
                switch (i18n.language) {
                  case "ru":
                    return category === "cast iron"
                      ? "Чугун"
                      : category === "polyethylene"
                        ? "Полиэтилен"
                        : category === "aluminum"
                          ? "Алюминий"
                          : category === "steel"
                            ? "Сталь"
                            : product.category;
                  default:
                    return (
                      product.category.charAt(0).toUpperCase() +
                      product.category.slice(1)
                    );
                }
              })()}
            </p>
          </div>
        </div>

        {/* Simplified Wave SVG */}
        <div className="absolute bottom-0 left-0 w-full">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1440 100"
            className="w-full h-auto fill-white"
          >
            <path d="M0,80 Q360,40 720,60 T1440,70 L1440,100 L0,100 Z" />
          </svg>
        </div>
      </section>

      <section className="py-12 md:py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
            {/* Левая часть — картинка продукта */}
            <div className="flex flex-col">
              <div className="bg-white/50 p-4 rounded-xl border border-gray-100 shadow-sm w-full">
                <img
                  src={
                    product.image?.startsWith("/images/products/")
                      ? product.image.replace("/images/products/", "/images/products/")
                      : product.image?.startsWith("/")
                        ? product.image
                        : `/${product.image}`
                  }
                  alt={product.title}
                  className="w-full object-contain rounded-md shadow-sm max-h-[400px]"
                />
              </div>

              {/* Request Quote Button - positioned under the product image */}
              <div className="mt-6">
                <QuoteRequestModal
                  productId={product.productId || ""}
                  productName={product.title}
                  trigger={
                    <Button className="btn-gradient text-white font-semibold px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 w-full">
                      {t('header.requestQuote', 'Request a Quote')}
                    </Button>
                  }
                />
              </div>
            </div>
            {/* Правая часть — инфа о продукте */}
            <div className="space-y-6">
              <div>
                <span
                  className="inline-block px-3 py-1.5 text-sm font-semibold rounded-full border shadow-sm mb-3"
                  style={{
                    backgroundColor: "white",
                    color: "#2D7EB6",
                    border: "1px solid #2D7EB6"
                  }}
                >
                  {(() => {
                    const category = product.category?.toLowerCase();
                    switch (i18n.language) {
                      case "ru":
                        return category === "cast iron"
                          ? "Чугун"
                          : category === "polyethylene"
                            ? "Полиэтилен"
                            : category === "aluminum"
                              ? "Алюминий"
                              : category === "steel"
                                ? "Сталь"
                                : product.category;
                      default:
                        return (
                          product.category.charAt(0).toUpperCase() +
                          product.category.slice(1)
                        );
                    }
                  })()}
                </span>
                <h2 className="text-2xl md:text-3xl font-bold mb-3">
                  {product.title}
                </h2>
                <div
                  className="prose prose-neutral max-w-none mb-6"
                  dangerouslySetInnerHTML={{
                    __html: product.description || "",
                  }}
                />
              </div>
              {/* Features */}
              {product.features && product.features.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-3">
                    {t("products.features", "Features")}
                  </h3>
                  <ul className="space-y-3">
                    {product.features.map((feature: string, index: number) => (
                      <li
                        key={index}
                        className="flex items-start gap-3 p-3 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-100 shadow-sm"
                      >
                        <span className="flex-shrink-0">
                          <Check className="w-5 h-5" style={{ color: "#2980B9" }} />
                        </span>
                        <span className="text-neutral-800 font-roboto">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {/* Applications */}
              {product.applications && product.applications.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-3">
                    {t("products.applications", "Applications")}
                  </h3>
                  <ul className="space-y-3">
                    {product.applications.map((application: string, index: number) => (
                      <li
                        key={index}
                        className="flex items-start gap-3 p-3 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-100 shadow-sm"
                      >
                        <span className="flex-shrink-0">
                          <Check className="w-5 h-5" style={{ color: "#2980B9" }} />
                        </span>
                        <span className="text-neutral-800 font-roboto">
                          {application}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {/* Specifications */}
              {product.specifications &&
                Object.keys(product.specifications).length > 0 && (
                  <div className="mb-8">
                    <h3 className="text-xl font-semibold mb-3">
                      {t("products.specifications", "Specifications")}
                    </h3>
                    <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                      <table className="w-full text-sm">
                        <tbody>
                          {Object.entries(product.specifications).map(
                            ([key, value]) => (
                              <tr
                                key={key}
                                className="border-b border-gray-200 last:border-0"
                              >
                                <td
                                  className="py-2.5 font-medium"
                                  style={{ color: "#2980B9" }}
                                >
                                  {key}
                                </td>
                                <td className="py-2.5 text-gray-800">
                                  {value}
                                </td>
                              </tr>
                            ),
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              {/* Documents */}
              {(product.documents as ProductDocument[] | undefined)?.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-3">
                    {t("product.documents", "Documents")}
                  </h3>
                  <div className="space-y-2">
                    {(product.documents as ProductDocument[]).map((doc) => (
                      <a
                        key={doc.id}
                        href={doc.fileUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                      >
                        <FileDown className="h-5 w-5 mr-3" style={{ color: "#2980B9" }} />
                        <div>
                          <p className="font-medium">{doc.title}</p>
                          {doc.description && (
                            <p className="text-sm text-gray-500">
                              {doc.description}
                            </p>
                          )}
                        </div>
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Bottom CTA Section */}
      <CtaSection />
    </>
  );
};

export default ProductDetail;
